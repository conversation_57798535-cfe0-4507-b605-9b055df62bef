# 用户添加平台账号 API 使用说明

## API 概述

用户添加平台账号API允许已认证的用户向系统中添加新的平台账号（如头条号、百家号）。系统支持自动获取真实的账号统计数据，并提供实时推送通知功能。

## 基本信息

- **端点**: `POST /api/user/platform-accounts`
- **认证**: 需要有效的用户会话（sessionId）
- **内容类型**: `application/json`
- **支持平台**: 头条号、百家号

## 认证要求

在调用此API之前，用户必须：
1. 已成功登录并获得有效的 `sessionId`
2. 会话未过期且状态有效
3. 具有添加平台账号的权限

## 请求格式

### HTTP 请求
```http
POST /api/user/platform-accounts
Content-Type: application/json

{
  "sessionId": "用户会话ID",
  "account_data": {
    "phone": "平台账号手机号",
    "platform": "平台名称",
    "login_type": "内容类型",
    "username": "用户名",
    "sessionid": "平台会话ID",
    "team_tag": "团队标签",
    "homepage_url": "主页链接",
    "is_verified": "实名认证状态",
    "drafts_count": "草稿数量",
    "account_status": "账号状态"
  },
  "current_holder_id": 123  // 可选：指定账号持有者ID
}
```

### 参数说明

#### 必需参数

| 参数名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `sessionId` | string | 用户会话ID | "abc123..." |
| `account_data.phone` | string | 平台账号手机号 | "***********" |
| `account_data.platform` | string | 平台名称 | "头条号" |
| `account_data.login_type` | string | 内容类型 | "文章" |
| `account_data.username` | string | 平台用户名 | "我的头条号" |
| `account_data.sessionid` | string | 平台会话ID | "session_abc123..." |

#### 可选参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `account_data.team_tag` | string | "" | 团队标签 |
| `account_data.homepage_url` | string | "" | 主页链接 |
| `account_data.is_verified` | string | "否" | 实名认证状态 |
| `account_data.drafts_count` | string | "-" | 草稿数量 |
| `account_data.account_status` | string | "正常" | 账号状态 |
| `current_holder_id` | number | 当前用户ID | 指定账号持有者 |

### 平台类型说明

支持的平台类型：
- `头条号` - 今日头条平台
- `` - 抖音短视频平台
- `微头条` - 微头条平台

### 内容类型说明

支持的内容类型：
- `文章` - 图文内容
- `视频` - 视频内容
- `微头条` - 短内容

## 响应格式

### 成功响应

```json
{
  "success": true,
  "message": "平台账号添加成功",
  "accountInfo": {
    "phone": "***********",
    "platform": "头条号",
    "username": "我的头条号",
    "stats": {
      "followers": "1000",
      "total_reads": "50000",
      "total_income": "100.50",
      "yesterday_reads": "500",
      "yesterday_income": "5.20",
      "credit_score": "95",
      "can_withdraw_amount": "80.30"
    },
    "created_at": "2024-01-01T10:00:00.000Z"
  }
}
```

### 错误响应

```json
{
  "success": false,
  "message": "错误描述",
  "error_code": "ERROR_CODE"
}
```

## 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 添加成功 |
| 400 | 请求参数错误 |
| 401 | 认证失败或会话无效 |
| 409 | 平台账号已存在 |
| 500 | 服务器内部错误 |

## 使用示例

### 示例1：添加头条号账号

```javascript
const response = await fetch('/api/user/platform-accounts', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    sessionId: "user_session_abc123",
    account_data: {
      phone: "***********",
      platform: "头条号",
      login_type: "文章",
      username: "我的头条号",
      sessionid: "toutiao_session_xyz789",
      team_tag: "A组",
      homepage_url: "https://www.toutiao.com/c/user/123456/",
      is_verified: "是",
      drafts_count: "5",
      account_status: "正常"
    }
  })
});

const result = await response.json();
console.log(result);
```

### 示例2：添加抖音账号并指定持有者

```javascript
const response = await fetch('/api/user/platform-accounts', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    sessionId: "user_session_abc123",
    account_data: {
      phone: "***********",
      platform: "抖音",
      login_type: "视频",
      username: "我的抖音号",
      sessionid: "douyin_session_xyz789",
      team_tag: "B组",
      account_status: "正常"
    },
    current_holder_id: 456  // 指定给用户ID为456的用户
  })
});

const result = await response.json();
console.log(result);
```

## 特殊功能

### 自动数据获取

当提供有效的 `sessionid` 时，系统会自动尝试获取平台的真实统计数据，包括：
- 粉丝数量
- 总阅读量
- 总收益
- 昨日阅读量
- 昨日收益
- 信用分
- 可提现金额

### 实时推送通知

添加成功后，系统会通过SSE（Server-Sent Events）向相关用户推送实时通知，包括：
- 主账号用户
- 相关子账号用户
- 账号持有者
- 管理员（如果在线）

#### SSE连接端点
```
GET /auth/sse?sessionId={sessionId}&clientId={clientId}
```

#### 推送消息格式

**1. 用户通知消息**
```json
{
  "type": "platform_account_received",
  "message": "您添加了新的平台账号: *********** (我的头条号)",
  "data": {
    "addedByUserId": 123,
    "addedByUserPhone": "***********",
    "addedByUserType": "主账号",
    "platformPhone": "***********",
    "platformUsername": "我的头条号",
    "timestamp": "2024-01-01T10:00:00.000Z",
    "isCurrentUser": true
  },
  "timestamp": "2024-01-01T10:00:00.000Z"
}
```

**2. 管理员通知消息**
```json
{
  "type": "platform_account_notification",
  "message": "用户 *********** 添加了新的平台账号: *********** (我的头条号)",
  "data": {
    "userId": 123,
    "userPhone": "***********",
    "platformPhone": "***********",
    "platformUsername": "我的头条号",
    "timestamp": "2024-01-01T10:00:00.000Z"
  },
  "timestamp": "2024-01-01T10:00:00.000Z"
}
```

**3. 平台数据更新推送**
```json
{
  "type": "new_platform_account_added",
  "data": {
    "mainAccountData": {
      "***********": {
        "phone": "***********",
        "platform": "头条号",
        "username": "我的头条号",
        "login_type": "文章",
        "team_tag": "A组",
        "stats": {
          "followers": "1000",
          "total_reads": "50000",
          "total_income": "100.50",
          "yesterday_reads": "500",
          "yesterday_income": "5.20",
          "credit_score": "95",
          "can_withdraw_amount": "80.30"
        },
        "account_status": "正常",
        "is_verified": "是",
        "owner_id": 123,
        "current_holder_id": 123,
        "created_at": "2024-01-01T10:00:00.000Z",
        "updated_at": "2024-01-01T10:00:00.000Z"
      }
    },
    "newAccountPhone": "***********",
    "timestamp": "2024-01-01T10:00:00.000Z"
  },
  "timestamp": "2024-01-01T10:00:00.000Z"
}
```

#### 推送消息类型说明

| 消息类型 | 目标用户 | 说明 |
|----------|----------|------|
| `platform_account_received` | 主账号体系内用户 | 通知用户有新的平台账号被添加 |
| `platform_account_notification` | 管理员 | 通知管理员用户添加了新账号 |
| `new_platform_account_added` | 主账号体系内用户 | 推送完整的新账号数据 |
| `platform_data_update` | 相关用户 | 推送账号数据更新 |

#### 前端SSE接收示例

```javascript
// 建立SSE连接
const eventSource = new EventSource(`/auth/sse?sessionId=${sessionId}&clientId=${clientId}`);

// 监听推送消息
eventSource.onmessage = function(event) {
  const data = JSON.parse(event.data);

  switch(data.type) {
    case 'platform_account_received':
      // 处理平台账号添加通知
      console.log('收到平台账号添加通知:', data.message);
      showNotification(data.message);
      break;

    case 'new_platform_account_added':
      // 处理新平台账号数据
      console.log('收到新平台账号数据:', data.data);
      updatePlatformAccountsList(data.data.mainAccountData);
      break;

    case 'platform_data_update':
      // 处理平台数据更新
      console.log('收到平台数据更新:', data.data);
      updateAccountStats(data.data);
      break;

    default:
      console.log('收到未知类型推送:', data);
  }
};

// 处理连接错误
eventSource.onerror = function(event) {
  console.error('SSE连接错误:', event);
  // 可以实现重连逻辑
};
```

## 错误处理

### 常见错误及解决方案

1. **会话无效 (401)**
   ```json
   {
     "success": false,
     "message": "无效的会话"
   }
   ```
   解决方案：重新登录获取新的sessionId

2. **平台账号已存在 (409)**
   ```json
   {
     "success": false,
     "message": "该手机号的平台账号已存在"
   }
   ```
   解决方案：检查手机号是否已被添加

3. **参数验证失败 (400)**
   ```json
   {
     "success": false,
     "message": "手机号和平台信息不能为空"
   }
   ```
   解决方案：检查必需参数是否完整

## 注意事项

1. **手机号唯一性**: 每个手机号在系统中只能添加一次
2. **会话有效期**: 确保sessionId在有效期内
3. **平台会话**: sessionid应为有效的平台登录会话
4. **权限控制**: 子账号只能看到归属于自己的平台账号
5. **数据更新**: 添加后系统会定期更新统计数据
6. **实时性**: 真实数据获取可能需要几秒钟时间
7. **推送通知**:
   - 需要建立SSE连接才能接收实时推送
   - 推送消息包含完整的账号数据和通知信息
   - 离线用户不会收到推送，需要重新获取数据
   - 推送失败不影响账号添加操作的成功
8. **数据同步**: 添加成功后会自动推送给主账号体系内的所有在线用户

## 最佳实践

1. 在添加前先验证用户会话状态
2. 提供有效的平台sessionid以获取真实数据
3. 合理设置team_tag便于账号管理
4. 定期检查账号状态和数据更新
5. 处理好错误情况和用户提示
6. **推送通知最佳实践**:
   - 在调用添加API前建立SSE连接
   - 实现推送消息的分类处理逻辑
   - 添加SSE连接断开重连机制
   - 对推送消息进行去重处理
   - 实现离线消息补偿机制
7. **用户体验优化**:
   - 添加操作后立即显示加载状态
   - 通过推送消息实时更新界面
   - 提供操作成功的明确反馈

## 相关API

- `GET /api/user/platform-accounts` - 获取平台账号列表
- `PUT /api/user/platform-accounts/{phone}` - 更新平台账号信息
- `DELETE /api/user/platform-accounts/{phone}` - 删除平台账号
- `POST /auth/check-session` - 检查会话状态
